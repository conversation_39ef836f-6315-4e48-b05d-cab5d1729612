import {
  KeyboardAvoidingView,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  DropdownForm,
  FAddressPickerForm,
  TextFieldForm,
} from '../../Default/form/component-form';
import {validatePhoneNumber} from '../../../utils/validate';
import DatePicker from 'react-native-date-picker';
import {TypoSkin} from '../../../assets/skin/typography';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';
import {DataController} from '../../../base/baseController';
import {FPopup, showPopup, Winicon} from 'wini-mobile-components';
import {PopupQrcodeScan} from '../../../features/qrcode-scanner/qrcode-scan';

export default function EditCustomer({methods}: {methods: any}) {
  const {t} = useTranslation();
  const [open, setOpen] = useState(false);
  const [disableRefCode, setDisableRefCode] = useState(false);
  const [date, setDate] = useState<any>(new Date());
  const customerController = new DataController('Customer');
  const popupRef = React.useRef<any>(null);

  useEffect(() => {
    if (methods.getValues('Dob')) {
      setDate(new Date(methods.getValues('Dob')));
    }
    if(methods.getValues('RefCode')){
      setDisableRefCode(true);
    }    
  }, []);

  return (
    <Pressable
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FPopup ref={popupRef} />
      <KeyboardAvoidingView behavior={'padding'} style={{flex: 1}}>
        <View
          style={{
            paddingHorizontal: 16,
            gap: 16,
            paddingVertical: 8,
            paddingBottom: 100,
          }}>
          <TextFieldForm
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.email')}
            label={t('profile.email')}
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="Email"
            type="email-address"
            textFieldStyle={{padding: 16}}
          />
          <TextFieldForm
            required
            disabled
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.phone')}
            label={t('profile.phone')}
            control={methods.control}
            register={methods.register}
            errors={methods.formState.errors}
            name="Mobile"
            textFieldStyle={{padding: 16}}
            type="number-pad"
            onBlur={(ev: string) => {
              // Check if the number doesn't already start with 0 or +84
              if (/^(\+84|0)/.test(ev)) {
                const val = validatePhoneNumber(ev);
                if (!val) {
                  methods.setError('Mobile', {
                    message: t('profile.invalidPhone'),
                  });
                  return;
                }
              }
              if (ev?.length !== 0) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: t('profile.phoneRequired'),
                });
            }}
          />
          <TouchableOpacity
            style={{width: '100%', gap: 8}}
            onPress={() => setOpen(true)}>
            <Text
              style={{
                ...TypoSkin.label3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {t('profile.selectBirthdate')}
            </Text>
            <View
              style={{
                width: '100%',
                backgroundColor: '#fff',
                borderRadius: 8,
                paddingHorizontal: 16,
                paddingVertical: 12,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
              }}>
              <Text>
                {Ultis.datetoString(new Date(methods.watch('Dob') ?? 0))}
              </Text>
            </View>
          </TouchableOpacity>
          <DatePicker
            modal
            open={open}
            date={date}
            mode="date"
            minimumDate={new Date('1900-01-01')}
            maximumDate={new Date()}
            onConfirm={selectedDate => {
              setOpen(false);
              setDate(selectedDate);
              methods.setValue('Dob', selectedDate.getTime());
            }}
            onCancel={() => {
              setOpen(false);
            }}
          />
          <FAddressPickerForm
            control={methods.control}
            errors={methods.formState.errors}
            name="Address"
            label={t('profile.address')}
            placeholder={t('profile.address')}
            textFieldStyle={{
              paddingLeft: 8,
              gap: 12,
            }}
            onChange={value => {
              methods.setValue('Long', value.geometry.location.lng);
              methods.setValue('Lat', value.geometry.location.lat);
              methods.setValue('Address', value.formatted_address);
              return value.formatted_address;
            }}
          />
          <DropdownForm
            placeholder={t('profile.gender')}
            label={t('profile.gender')}
            control={methods.control}
            errors={methods.formState.errors}
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            name="Gender"
            options={[
              {id: 1, name: t('profile.male')},
              {id: 2, name: t('profile.female')},
            ]}
          />
          <TextFieldForm
            disabled={disableRefCode}
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={'Mã giới thiệu (Nếu có)'}
            label={'Mã giới thiệu (Nếu có)'}
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="RefCode"
            textFieldStyle={{padding: 16}}
            suffix={
              <TouchableOpacity
                disabled={disableRefCode}
                style={{paddingVertical: 12}}
                onPress={() => {
                  showPopup({
                    ref: popupRef,
                    enableDismiss: true,
                    children: (
                      <PopupQrcodeScan
                        ref={popupRef}
                        onDone={(data: any) => {
                          methods.setValue('RefCode', data);
                        }}
                      />
                    ),
                  });
                }}>
                <Winicon src={`outline/touch gestures/scan`} size={20} />
              </TouchableOpacity>
            }
          />
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.name')}
            label={t('profile.name')}
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="Name"
            textFieldStyle={{padding: 16}}
          />
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
}
